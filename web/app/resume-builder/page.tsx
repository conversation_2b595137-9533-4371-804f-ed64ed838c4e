"use client";

import React from "react";
import { useState, useCallback, ChangeEvent, useEffect, useRef } from "react";
import { z } from "zod";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Seo from "@/components/Seo";
import { useAuth } from "@/hooks/useAuth";
import { startGeneration, useGenerationStatus } from "@/hooks/useResumeGeneration";
import ResumePreview from "@/components/ResumePreview";
import SuggestionList from "@/components/SuggestionList";
import { useResume } from "@/hooks/useResume";
import Toast from "@/components/Toast";
import TemplatePreviewThumbnail from "@/components/TemplatePreviewThumbnail";
import { ResumeTemplate, resumeTemplates } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import ResumeEditForm from "@/components/ResumeEditForm";
import { StructuredResumeData } from "@/types/resume-structured";
import ResumeBuilderLanding from "@/components/ResumeBuilderLanding";
import { createEmptyResumeStructure } from "@/utils/emptyResumeStructure";
import { motion, AnimatePresence } from 'framer-motion';
import { useStepNavigation, ThreeSteps } from '@/hooks/useStepNavigation';
import { useAnalytics } from '@/hooks/useAnalytics';
import { apiFetch, apiGet, apiPost } from '@/lib/apiFetch';
import IndependenceDayBanner from '@/components/IndependenceDayBanner';
import { downloadResumePDF } from "@/utils/downloadFunctions";

// --- Icon Components ---
const CheckIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
);
const UploadCloudIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
);
const FileTextIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
);
const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
);
const ArrowRightIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" /></svg>
);
const Spinner = ({ className }: { className?: string }) => (
  <svg className={`animate-spin ${className}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);
const ArrowLeftIcon = ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h17" /></svg>
);
const PencilIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z" /></svg>
);
const DownloadIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
);
const ExclamationTriangleIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
);
const TemplateIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" /></svg>
);
const TokenIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
    <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
    <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
    <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
  </svg>
);

// --- Insufficient Tokens Banner Component ---
interface InsufficientTokensBannerProps {
  show: boolean;
  message: string;
  onDismiss: () => void;
  onBuyTokens: () => void;
}

function InsufficientTokensBanner({ show, message, onDismiss, onBuyTokens }: InsufficientTokensBannerProps) {
  if (!show) return null;

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6 animate-fade-in">
      <div className="flex items-start gap-3">
        <ExclamationTriangleIcon className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-amber-800 mb-1">
            Token Tidak Cukup
          </h3>
          <p className="text-sm text-amber-700 mb-3">
            {message}
          </p>
          <div className="flex gap-2">
            <button
              onClick={onBuyTokens}
              className="inline-flex items-center gap-1 px-3 py-1.5 bg-amber-600 hover:bg-amber-700 text-white text-sm font-medium rounded-md transition-colors"
            >
              <TokenIcon className="w-4 h-4" />
              Beli Token
            </button>
            <button
              onClick={onDismiss}
              className="px-3 py-1.5 text-amber-700 hover:text-amber-800 text-sm font-medium rounded-md hover:bg-amber-100 transition-colors"
            >
              Tutup
            </button>
          </div>
        </div>
        <button
          onClick={onDismiss}
          className="text-amber-400 hover:text-amber-600 transition-colors"
        >
          <XIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}

// --- Step Indicator Component ---
function StepIndicator({ step, currentStep, label }: { step: number; currentStep: number; label: string }) {
  const isActive = step === currentStep;
  const isCompleted = step < currentStep;

  return (
    <div className="flex flex-col items-center gap-2 relative text-center min-w-0">
      <div
        className={`w-10 h-10 flex items-center justify-center rounded-full border-2 font-bold transition-all duration-300 flex-shrink-0 ${
          isCompleted ? "bg-primary border-primary text-white" : isActive ? "border-primary text-primary scale-110" : "border-gray-300 text-gray-400"
        }`}
      >
        {isCompleted ? <CheckIcon className="w-6 h-6" /> : step}
      </div>
      <span className={`font-semibold text-xs sm:text-sm whitespace-nowrap ${isActive || isCompleted ? "text-gray-900" : "text-gray-500"}`}>{label}</span>
    </div>
  );
}

// --- Validation Schema ---
const resumeScratchSchema = z.object({
  fullName: z.string().optional(),
  professionalTitle: z.string().optional(),
  professionalSummary: z.string().optional(),
  mostRecentJob: z.object({
    title: z.string().optional(),
    company: z.string().optional(),
    achievements: z.string().optional(),
  }).optional(),
  skills: z.string().optional(),
});

type ResumeFormData = z.infer<typeof resumeScratchSchema>;

type FormErrors = Partial<Record<keyof ResumeFormData | 'mostRecentJob.title' | 'mostRecentJob.company' | 'mostRecentJob.achievements' | 'general', string>>;

// --- Bottom Sheet Component ---
interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

function BottomSheet({ isOpen, onClose, title, children }: BottomSheetProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleDragEnd = (event: any, info: any) => {
    // Close if dragged down more than 150px or with sufficient velocity
    if (info.offset.y > 150 || info.velocity.y > 500) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />

          {/* Bottom Sheet */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed inset-x-0 bottom-0 z-50 md:hidden"
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.5 }}
            onDragEnd={handleDragEnd}
            whileDrag={{ cursor: 'grabbing' }}
          >
            <div className="bg-white rounded-t-xl shadow-lg max-h-[80vh] flex flex-col">
              {/* Header with drag indicator */}
              <div className="flex flex-col items-center">
                {/* Drag Handle */}
                <div className="w-12 h-1 bg-gray-300 rounded-full mt-3" />
                
                <div className="flex items-center justify-between w-full p-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                  <button
                    onClick={onClose}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                  >
                    <XIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

// --- Main Page Component ---
export default function ResumeBuilderPage() {
  const auth = useAuth();
  const { trackEvent, logError } = useAnalytics();
  const { user, signIn, signUp, signInWithGoogle } = auth;
  
  // Initialize userPath - always start with null to avoid hydration mismatch
  const [userPath, setUserPath] = useState<'ai-powered' | 'manual' | null>(null);
  
  const [errors, setErrors] = useState<FormErrors>({});
  
  // State for insufficient tokens error banner
  const [insufficientTokensError, setInsufficientTokensError] = useState<{
    show: boolean;
    message: string;
    context: 'manual' | 'ai-powered' | 'download';
  }>({
    show: false,
    message: '',
    context: 'download'
  });
  
  // Permanent flag to track if there was ever a token error (not affected by banner dismissal)
  const [hasTokenError, setHasTokenError] = useState<boolean>(false);

  // Login/Register modal state
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginMode, setLoginMode] = useState<'login' | 'register'>('register');
  const [loginForm, setLoginForm] = useState({ email: '', password: '', confirmPassword: '' });
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  
  // Ref for step indicator area
  const stepIndicatorRef = useRef<HTMLDivElement>(null);
  
  // Step navigation with browser history support
  const stepNav = useStepNavigation<ThreeSteps>({
    initialStep: userPath === 'manual' ? 3 : 1,
    totalSteps: 3,
    stepParam: 'resume-step', // Use unique step parameter for resume builder
    onStepValidation: async (currentStep, targetStep) => {
      if (currentStep === 1 && targetStep === 2) {
        // Validate step 1 completion
        if (resumeInputMethod === 'upload') {
          if (!hasUploadedResume) {
            setErrors({ general: "Silakan unggah CV/resume Anda" });
            return false;
          }
        } else if (resumeInputMethod === 'scratch') {
          const validationResult = resumeScratchSchema.safeParse(formData);
          if (!validationResult.success) {
            const newErrors: FormErrors = {};
            validationResult.error.errors.forEach((err) => {
              const path = err.path.join('.') as keyof FormErrors;
              newErrors[path] = err.message;
            });
            setErrors(newErrors);
            return false;
          }
        }
      }
      if (currentStep === 2 && targetStep === 3) {
        // Validate step 2 completion
        if (jobInputMethod === "text" && !jobText.trim()) {
          setToast({ show: true, message: "Deskripsi pekerjaan wajib diisi", type: "error" });
          return false;
        }
        if (jobInputMethod === "image" && !jobImage) {
          setToast({ show: true, message: "Gambar deskripsi pekerjaan wajib diunggah", type: "error" });
          return false;
        }
      }
      return true;
    },
    onStepChange: (newStep, prevStep) => {
      // Clear any previous errors and banners when changing steps
      setErrors({});
      setInsufficientTokensError({ show: false, message: '', context: 'download' });
      setHasTokenError(false);
      setLocalGenerationError(null);
      
      // Scroll to step indicator if user is scrolled below it
      if (stepIndicatorRef.current) {
        const stepIndicatorRect = stepIndicatorRef.current.getBoundingClientRect();
        const isStepIndicatorAboveViewport = stepIndicatorRect.top < 0;
        
        if (isStepIndicatorAboveViewport) {
          stepIndicatorRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
      
      // Track step navigation
      trackEvent('Resume Builder Step Navigation', {
        from: prevStep,
        to: newStep,
        userPath: userPath || 'unknown'
      });
    },
  });

  // Extract step navigation methods and current step
  const { currentStep, goNext, goBack: originalGoBack, goToStep } = stepNav;
  
  // Custom goBack function that handles returning to path selection
  const goBack = useCallback(() => {
    // If we're on step 1 of AI-powered path or step 3 of manual path, go back to path selection
    if ((userPath === 'ai-powered' && currentStep === 1) || (userPath === 'manual' && currentStep === 3)) {
      // Clear URL parameters and reset to path selection
      const url = new URL(window.location.href);
      url.searchParams.delete('resume-step');
      url.searchParams.delete('path');
      window.history.pushState({}, '', url.toString());
      
      // Reset state
      setUserPath(null);
      setStructuredData(null);
      setSelectedTemplate(null);
      setGenerationId(null);
      setErrors({});
      setInsufficientTokensError({ show: false, message: '', context: 'download' });
      setHasTokenError(false);
      setLocalGenerationError(null);
      
      return;
    }
    
    // Otherwise use the original goBack function
    originalGoBack();
  }, [userPath, currentStep, originalGoBack]);

  // Step 3 template selection state
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);

  // Step 4 live editing state
  const [structuredData, setStructuredData] = useState<StructuredResumeData | null>(null);
  const [liveHtmlContent, setLiveHtmlContent] = useState<string>("");
  const autoSaveTimer = useRef<NodeJS.Timeout | null>(null);
  const [hasRequiredFields, setHasRequiredFields] = useState<boolean>(false);

  // Overflow detection state
  const [isContentOverflowing, setIsContentOverflowing] = useState<boolean>(false);

  // Bottom sheet states
  const [isTemplateSheetOpen, setIsTemplateSheetOpen] = useState(false);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  
  // Template selection loading state
  const [isSelectingTemplate, setIsSelectingTemplate] = useState(false);

  // Resume hook (handles upload, view, delete, existing resume detection)
  const {
    isUploading,
    uploadSuccess,
    error: resumeError,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  } = useResume(auth);

  const hasUploadedResume = uploadSuccess && !!existingResume;

  // Step 1 state
  const [resumeInputMethod, setResumeInputMethod] = useState<"upload" | "scratch">("upload");
  const [resumeFile, setResumeFile] = useState<File | null>(null);

  // Step 1 state (scratch)
  const [formData, setFormData] = useState<ResumeFormData>({
    fullName: "",
    professionalTitle: "",
    professionalSummary: "",
    mostRecentJob: {
      title: "",
      company: "",
      achievements: "",
    },
    skills: "",
  });

  // Step 2 state
  const [jobInputMethod, setJobInputMethod] = useState<"text" | "image">("text");
  const [jobText, setJobText] = useState("");
  const [jobImage, setJobImage] = useState<File | null>(null);

  // Step 3 generation state
  const [isDownloadingPdf, setIsDownloadingPdf] = useState(false);
  const [isCreatingManualResume, setIsCreatingManualResume] = useState(false);
  
  // Token deduction status for current resume
  const [tokensDeducted, setTokensDeducted] = useState<boolean>(false);

  // Check token deduction status for a resume
  const checkTokenDeductionStatus = useCallback(async (resumeId: string) => {
    if (!resumeId || !auth.user?.id) return;

    try {
      const response = await apiGet(`/api/resume/${resumeId}`, {
        method: 'GET',
      });

      if (response.ok) {
        const resumeData = await response.json();
        // Set tokens deducted status based on response
        setTokensDeducted(resumeData.tokens_deducted || false);
      } else {
        console.error('Failed to fetch resume status');
      }
    } catch (error) {
      console.error('Error checking token deduction status:', error);
    }
  }, [auth.user?.id]);

  // Download resume as PDF using edge function
  const downloadResumePdf = async (resumeId: string) => {
    if (!resumeId) {
      setToast({ show: true, message: "Resume ID tidak ditemukan", type: "error" });
      return;
    }

    trackEvent('Resume Download Attempt', {
      format: 'pdf',
      resumeId: resumeId
    });

    setIsDownloadingPdf(true);

    try {
      const pdfBlob = await downloadResumePDF(resumeId);

      const downloadLink = document.createElement('a');
      const url = window.URL.createObjectURL(pdfBlob);
      downloadLink.href = url;
      const fileName = `CV_Gigsta_${resumeId}`;
      downloadLink.download = `${fileName}.pdf`;
      document.body.appendChild(downloadLink);
      downloadLink.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(downloadLink);

      trackEvent('Resume Downloaded', {
        format: 'pdf',
        resumeId: resumeId
      });

      // For manual resumes, mark tokens as deducted after successful download
      if (userPath === 'manual') {
        setTokensDeducted(true);
      }
    } catch (err) {
      // Check if it's an insufficient tokens error
      const errorMessage = err instanceof Error ? err.message : 'Gagal mengunduh PDF. Silakan coba lagi.';
      if (errorMessage.includes('Token') || errorMessage.includes('tidak cukup')) {
        setInsufficientTokensError({
          show: true,
          message: errorMessage,
          context: 'download'
        });
        setHasTokenError(true);
      } else {
        setToast({ show: true, message: errorMessage, type: 'error' });
      }

      trackEvent('Resume Download Error', {
        error: errorMessage,
        format: 'pdf',
        resumeId: resumeId
      });

      if (!errorMessage.includes('Token') && !errorMessage.includes('tidak cukup')) {
        logError(err instanceof Error ? err : new Error('Failed to download resume PDF'), {
          feature: 'resume_builder',
          action: 'download_pdf'
        }, 'critical');
      }
    } finally {
      setIsDownloadingPdf(false);
    }
  };

  // Handle insufficient tokens banner actions
  const handleBuyTokens = useCallback(() => {
    window.open('/buy-tokens', '_blank');
  }, []);

  const handleDismissBanner = useCallback(() => {
    setInsufficientTokensError({
      show: false,
      message: '',
      context: 'download'
    });
  }, []);

  const [generationId, setGenerationId] = useState<string | null>(null);
  const generation = useGenerationStatus(generationId);
  
  // Local error state for immediate UI feedback
  const [localGenerationError, setLocalGenerationError] = useState<string | null>(null);

  const handleDownload = React.useCallback(() => {
    if (!generationId) return;
    downloadResumePdf(generationId);
  }, [generationId]);

  // Check token deduction status when generationId changes
  useEffect(() => {
    if (generationId && userPath === 'manual') {
      checkTokenDeductionStatus(generationId);
    }
  }, [generationId, userPath, checkTokenDeductionStatus]);

  // Remove path parameter from URL on mount (step parameter is handled by useStepNavigation)
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);

    // Always remove the path parameter if present (resume-step is handled by useStepNavigation hook)
    if (urlParams.has('path')) {
      urlParams.delete('path');
      const newUrl = new URL(window.location.href);
      newUrl.search = urlParams.toString();

      // Replace the current URL without adding to history
      window.history.replaceState(
        null,
        '',
        newUrl.toString()
      );
    }
  }, []); // Empty dependency array - only run once on mount

  // Handle browser back/forward navigation for path changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handlePopState = (event: PopStateEvent) => {
      const urlParams = new URLSearchParams(window.location.search);
      const pathFromURL = urlParams.get('path');
      const stepFromURL = urlParams.get('resume-step');
      
      // If no path in URL, reset to path selection
      if (!pathFromURL) {
        setUserPath(null);
        setStructuredData(null);
        setSelectedTemplate(null);
        setGenerationId(null);
        setErrors({});
        setInsufficientTokensError({ show: false, message: '', context: 'download' });
        setHasTokenError(false);
        return;
      }
      
      // Update userPath if it changed via browser navigation
      if (pathFromURL !== userPath && (pathFromURL === 'ai-powered' || pathFromURL === 'manual')) {
        setUserPath(pathFromURL);
        
        // Initialize manual path if navigating to it
        if (pathFromURL === 'manual' && stepFromURL === '3' && !structuredData) {
          const emptyStructure = createEmptyResumeStructure();
          setStructuredData(emptyStructure);
          
          const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
          setSelectedTemplate(defaultTemplate);
        }
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [userPath, structuredData]);

  // Handle user login completion (for OAuth flow)
  useEffect(() => {
    if (user && showLoginModal) {
      // User just logged in, close modal
      setShowLoginModal(false);
      setLoginForm({ email: '', password: '', confirmPassword: '' });
      setLoginError('');
      setLoginSuccess('');
      // Show success toast when OAuth login completes
      setToast({ show: true, message: 'Berhasil masuk', type: 'success' });
    }
  }, [user, showLoginModal, setToast]);

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError('');
    setLoginSuccess('');

    try {
      // For registration, check if passwords match
      if (loginMode === 'register' && loginForm.password !== loginForm.confirmPassword) {
        setLoginError('Password tidak cocok');
        setIsLoggingIn(false);
        return;
      }

      const result = loginMode === 'login'
        ? await signIn(loginForm.email, loginForm.password)
        : await signUp(loginForm.email, loginForm.password);

      if (result.success) {
        if (loginMode === 'register') {
          // Show email confirmation message and switch to login mode
          setLoginMode('login');
          setLoginForm({ email: loginForm.email, password: '', confirmPassword: '' });
          // Show success message for registration
          setLoginSuccess('Pendaftaran berhasil! Silakan periksa email Anda untuk konfirmasi, lalu kembali ke tab ini untuk masuk');
        } else {
          // For login success
          setShowLoginModal(false);
          setLoginForm({ email: '', password: '', confirmPassword: '' });
          // Show success toast
          setToast({ show: true, message: 'Berhasil masuk', type: 'success' });
        }
      } else {
        setLoginError(result.message || 'Terjadi kesalahan saat masuk');
      }
    } catch (err) {
      setLoginError('Terjadi kesalahan saat masuk');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle Google login
  const handleGoogleLogin = async () => {
    setIsLoggingIn(true);
    try {
      const result = await signInWithGoogle(encodeURIComponent(window.location.pathname));
      if (result.success && result.message) {
        // Open Google OAuth in new tab to preserve context
        window.open(result.message, '_blank');
        setLoginSuccess('Silakan selesaikan login di tab baru, lalu kembali ke halaman ini');
      }
      if (result.error) {
        if (result.message) {
          setLoginError(result.message);
        } else {
          throw result.error;
        }
      }
    } catch (error: any) {
      setLoginError('Gagal login dengan Google');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle path selection
  const handlePathSelection = useCallback(async (path: 'ai-powered' | 'manual') => {
    // Check if user is logged in first for manual path
    if (path === 'manual' && !user) {
      setLoginMode('register');
      setShowLoginModal(true);
      return;
    }

    // Prevent multiple rapid calls
    if (isCreatingManualResume) {
      console.log('Manual resume creation already in progress, ignoring duplicate call');
      return;
    }

    setUserPath(path);

    // Add path to URL for proper browser history support
    const url = new URL(window.location.href);
    url.searchParams.set('path', path);

    if (path === 'manual') {
      // For manual path, immediately create resume record and skip to Step 3
      url.searchParams.set('resume-step', '3');
      const emptyStructure = createEmptyResumeStructure();
      setStructuredData(emptyStructure);

      // Set default template
      const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
      setSelectedTemplate(defaultTemplate);

      // Update URL and navigate to step 3 immediately
      window.history.pushState({ path, step: 3 }, '', url.toString());
      await goToStep(3);

      // Scroll to step content after transitioning to manual path
      // Use setTimeout to ensure the step content is rendered
      setTimeout(() => {
        // Find the step content card and scroll to it
        const stepContentCard = document.querySelector('.bg-white.p-4.sm\\:p-8.lg\\:p-10.rounded-xl.shadow-sm.border.border-gray-200');
        if (stepContentCard) {
          stepContentCard.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100); // Small delay to ensure DOM is updated

      // Now create resume record in the background with loading state
      try {
        setIsCreatingManualResume(true);

        // Create resume record
        const response = await apiFetch('/api/resume/manual', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            structured_data: emptyStructure,
            template_id: defaultTemplate.id
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create resume');
        }

        const result = await response.json();
        setGenerationId(result.id);
        
        // For new manual resumes, tokens haven't been deducted yet
        setTokensDeducted(false);

        setToast({ show: true, message: "CV siap untuk diisi!", type: "success" });
      } catch (error) {
        console.error('Error creating resume:', error);
        const errorMessage = error instanceof Error ? error.message : "Gagal membuat CV";

        // If it's a token error, show banner instead of toast
        if (errorMessage.includes('Token Anda tidak cukup')) {
          setInsufficientTokensError({
            show: true,
            message: errorMessage,
            context: 'manual'
          });
          setHasTokenError(true);
          // Don't show toast for token errors, the banner will handle it
          setToast({ show: false, message: '', type: 'success' });
        } else {
          // For other errors, show toast
          setToast({
            show: true,
            message: 'Gagal dalam mempersiapkan CV. Silakan coba lagi',
            type: "error"
          });
        }

        // Reset to path selection on error
        setUserPath(null);
        // Clear any generation state
        setGenerationId(null);
        setStructuredData(null);
        setLiveHtmlContent("");
      } finally {
        setIsCreatingManualResume(false);
      }
    } else {
      // For AI-powered path, start with Step 1
      url.searchParams.set('resume-step', '1');
      window.history.pushState({ path, step: 1 }, '', url.toString());
      await goToStep(1);
      
      // Scroll to step indicator after transitioning to AI-powered path
      // Use setTimeout to ensure the step indicator is rendered
      setTimeout(() => {
        if (stepIndicatorRef.current) {
          const stepIndicatorRect = stepIndicatorRef.current.getBoundingClientRect();
          const isStepIndicatorAboveViewport = stepIndicatorRect.top < 0;
          
          if (isStepIndicatorAboveViewport) {
            stepIndicatorRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      }, 100); // Small delay to ensure DOM is updated
    }
  }, [goToStep, isCreatingManualResume, user]);

  // Template selection handler
  const handleTemplateSelection = useCallback(async (template: ResumeTemplate) => {
    if (template.id === selectedTemplate?.id) return false;

    // Set loading state
    setIsSelectingTemplate(true);

    try {
      setSelectedTemplate(template);

      // Regenerate resume with new template
      if (structuredData) {
        try {
          const html = fillResumeTemplate(template, structuredData);
          setLiveHtmlContent(html);
          setHasRequiredFields(true);

          // Save template selection to Supabase
          if (generationId && auth.user?.id) {
            try {
              const response = await apiFetch(`/api/resume/${generationId}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  structured_data: structuredData,
                  template_id: template.id
                })
              });

              if (response.ok) {
                console.log("Template selection saved to Supabase");
              } else {
                console.error("Failed to save template selection to Supabase");
              }
            } catch (error) {
              console.error("Error saving template selection:", error);
            }
          }

          setToast({ show: true, message: `Template berhasil diganti ke ${template.name}`, type: "success" });
          return true;
        } catch (error) {
          console.error('Gagal mengganti template:', error);
          setLiveHtmlContent("");
          setHasRequiredFields(false);
          setToast({ show: true, message: "Gagal mengganti template", type: "error" });
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error('Template selection error:', error);
      setToast({ show: true, message: "Gagal memilih template", type: "error" });
      return false;
    } finally {
      // Clear loading state
      setIsSelectingTemplate(false);
    }
  }, [selectedTemplate, structuredData, generationId, auth.user?.id]);

  // Auto-save functionality
  const handleAutoSave = useCallback(async (data: StructuredResumeData) => {
    try {
      if (generationId && auth.user?.id) {
        const response = await apiFetch(`/api/resume/${generationId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            structured_data: data,
            template_id: selectedTemplate?.id || 'clean-professional'
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log("Resume auto-saved successfully:", result);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Auto-save failed');
        }
      } else {
        console.log("No generation ID or user - skipping auto-save");
      }
    } catch (error) {
      console.error("Auto-save failed:", error);
      // Don't show error toast for auto-save failures to avoid annoying users
    }
  }, [auth.user?.id, generationId, selectedTemplate]);

  // Handle overflow detection from ResumePreview
  const handleOverflowDetected = useCallback((isOverflowing: boolean) => {
    setIsContentOverflowing(isOverflowing);

    // Show toast when overflow is detected
    if (isOverflowing) {
      setToast({
        show: true,
        message: "Konten melebihi satu halaman. Silakan kurangi konten agar muat dalam satu halaman",
        type: "error"
      });
    }
  }, []);

  // Live preview update with debouncing
  const updateLivePreview = useCallback(async (data: StructuredResumeData) => {
    if (!selectedTemplate) return;
    
    // Clear existing timer to prevent multiple timers running
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }
    
    const timer = setTimeout(async () => {
      try {
        const html = fillResumeTemplate(selectedTemplate, data);
        setLiveHtmlContent(html);
        setHasRequiredFields(true);
      } catch (error) {
        console.error("Gagal memperbarui pratinjau langsung:", error);
        setLiveHtmlContent("");
        setHasRequiredFields(false);
      }
    }, 300); // 300ms debounce
    
    autoSaveTimer.current = timer;
  }, [selectedTemplate]);

  // Handle structured data changes
  const handleStructuredDataChange = useCallback((newData: StructuredResumeData) => {
    setStructuredData(newData);
    updateLivePreview(newData);
  }, [updateLivePreview]);

  // Process generation result to extract structured data
  useEffect(() => {
    if (generation.status === "done" && generationId) {
      // Try to use structured data from generation response first
      let finalStructuredData: StructuredResumeData;
      
      if (structuredData) {
        // Use structured data from local state
        finalStructuredData = structuredData;
      } else if (generation.structuredData) {
        // Use structured data from API response
        finalStructuredData = {
          ...generation.structuredData,
          metadata: {
            generatedAt: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            templateId: selectedTemplate?.id,
            ...generation.structuredData.metadata
          }
        };
      } else {
        // Fallback: create structured data from form data
        finalStructuredData = {
          personalInfo: {
            fullName: formData.fullName || "",
            email: "",
            phone: "",
            location: ""
          },
          professionalSummary: formData.professionalSummary || "",
          targetPosition: formData.professionalTitle || "",
          experiences: [{
            id: "exp-1",
            jobTitle: formData.mostRecentJob?.title || "",
            company: formData.mostRecentJob?.company || "",
            location: "",
            startDate: "",
            endDate: "",
            responsibilities: formData.mostRecentJob?.achievements ?
              formData.mostRecentJob.achievements.split('\n').filter(line => line.trim()) :
              []
          }],
          education: [{
            id: "edu-1",
            degree: "",
            institution: "",
            graduationDate: ""
          }],
          skills: {
            categories: [{
              category: "Keahlian",
              skills: formData.skills ? formData.skills.split(',').map(s => s.trim()) : [""]
            }]
          },
          metadata: {
            generatedAt: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            templateId: selectedTemplate?.id
          }
        };
      }
      
      setStructuredData(finalStructuredData);
    } else if (generation.status === "error" && generationId) {
      // Handle error status
      const errorMessage = "Terjadi kesalahan saat membuat CV. Silakan coba lagi.";
      setToast({ show: true, message: errorMessage, type: "error" });
      console.error('Kesalahan pembuatan resume:', generation.error);
    }
  }, [generation.status, generationId, formData, selectedTemplate?.id, generation.status === "done" ? generation.structuredData : undefined]);

  // Initialize live preview when structured data and template are available
  useEffect(() => {
    if (structuredData && selectedTemplate) {
      updateLivePreview(structuredData);
    }
  }, [structuredData, selectedTemplate]); // Remove updateLivePreview from dependencies to avoid infinite loop

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimer.current) {
        clearTimeout(autoSaveTimer.current);
      }
    };
  }, []);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, field: keyof ResumeFormData | `mostRecentJob.${keyof NonNullable<ResumeFormData['mostRecentJob']>}`) => {
    const { value } = e.target;
    const fieldParts = field.split('.');

    if (fieldParts.length > 1) {
        const [parent, child] = fieldParts as ['mostRecentJob', keyof NonNullable<ResumeFormData['mostRecentJob']>];
        setFormData(prev => ({
            ...prev,
            [parent]: {
                ...prev[parent],
                [child]: value
            }
        }));
    } else {
        setFormData(prev => ({ ...prev, [field as keyof ResumeFormData]: value }));
    }

    if (errors[field as keyof typeof errors]) {
        setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };


  const handleFileChange = (files: FileList | null) => {
    if (files && files.length > 0) {
      setResumeFile(files[0]);
    }
  };

  const onDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    handleFileChange(event.dataTransfer.files);
  }, []);

  const onDragOver = (event: React.DragEvent<HTMLDivElement>) => event.preventDefault();

  return (
    <main className="min-h-screen flex flex-col pt-16 bg-gray-50">
      {/* Toast notifications */}
      {toast.show && (
        <Toast
          show={toast.show}
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({ ...toast, show: false })}
        />
      )}
      <Seo
        title="CV ATS Friendly - Gigsta"
        description="Buat CV yang mudah dibaca oleh sistem ATS menggunakan AI dan meningkatkan peluang lolos seleksi."
        canonical="https://gigsta.io/resume-builder"
      />
      <Navbar auth={auth} />

      {/* Sticky Banner for Unauthenticated Users */}
      {!auth.loading && !auth.user && (
        <div className="sticky top-[4.5rem] z-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white py-2 sm:py-3 px-4 shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                <div className="relative">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-300 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                  </svg>
                  <div className="absolute inset-0 w-4 h-4 sm:w-5 sm:h-5 bg-yellow-300 rounded-full opacity-20 animate-ping"></div>
                </div>
                <span className="font-bold text-sm sm:text-base">🎉</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 min-w-0">
                <span className="font-bold text-xs sm:text-sm md:text-base leading-tight">
                  BONUS 10 TOKEN GRATIS!
                </span>
                <span className="text-xs sm:text-sm opacity-90 leading-tight">
                  untuk pengguna baru yang mendaftar
                </span>
              </div>
            </div>
            <button
              onClick={() => {
                setLoginMode('register');
                setShowLoginModal(true);
              }}
              className="bg-white text-purple-600 hover:bg-yellow-100 font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm text-center transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex-shrink-0"
            >
              Daftar Sekarang
            </button>
          </div>
        </div>
      )}

      {/* Independence Day Banner for Authenticated Users */}
      <IndependenceDayBanner />

      {/* Page Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-3 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">CV ATS Friendly</h1>
            <p className="mt-1 text-sm sm:text-base text-gray-600">
              Buat CV ATS-friendly yang lolos seleksi dengan mudah dan menarik perhatian perekrut
            </p>
        </div>
      </header>

      {/* Wizard */}
      <div className="flex-grow w-full max-w-6xl mx-auto px-3 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8 pb-20 md:pb-4">
        {/* Show landing page if no path is selected */}
        {!userPath && (
          <div className="bg-white p-4 sm:p-8 lg:p-10 rounded-xl shadow-sm border border-gray-200">
            <ResumeBuilderLanding onSelectPath={handlePathSelection} />
          </div>
        )}

        {/* Show step wizard for selected path */}
        {userPath && (
          <>
            {/* Step Indicator Bar - only show for AI-powered path */}
            {userPath === 'ai-powered' && (
              <div ref={stepIndicatorRef} className="flex items-center justify-center mb-8 relative">
                <div className="flex items-center justify-between w-full max-w-2xl">
                  <StepIndicator step={1} currentStep={currentStep} label="Unggah CV/Resume" />
                  <div className={`flex-1 h-0.5 mx-4 ${currentStep > 1 ? 'bg-primary' : 'bg-gray-300'}`}></div>
                  <StepIndicator step={2} currentStep={currentStep} label="Info Pekerjaan" />
                  <div className={`flex-1 h-0.5 mx-4 ${currentStep > 2 ? 'bg-primary' : 'bg-gray-300'}`}></div>
                  <StepIndicator step={3} currentStep={currentStep} label="Hasil & Edit" />
                </div>
              </div>
            )}

            {/* Step Content Card */}
            <div className="bg-white p-4 sm:p-8 lg:p-10 rounded-xl shadow-sm border border-gray-200">
          {currentStep === 1 && userPath === 'ai-powered' && (
            <div>
              <h2 className="text-2xl font-bold mb-2 text-gray-900">Langkah 1: Siapkan CV Anda</h2>
              <p className="text-gray-600 mb-8">Mulai dengan mengunggah CV/resume yang ada atau buat dari awal.</p>

              <div className="flex w-full p-1 bg-gray-100 rounded-xl border-2 border-gray-200 mb-8">
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${resumeInputMethod === "upload" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setResumeInputMethod("upload");
                    setErrors({});
                  }}
                >
                  <UploadCloudIcon className="w-5 h-5"/>
                  <span>Unggah CV/Resume</span>
                </button>
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${resumeInputMethod === "scratch" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setResumeInputMethod("scratch");
                    setErrors({});
                  }}
                >
                  <FileTextIcon className="w-5 h-5"/>
                  <span>Buat Dari Awal</span>
                </button>
              </div>

              {resumeInputMethod === "upload" ? (
                <div>
                  {isLoading ? (
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                      <Spinner className="h-12 w-12 text-primary" />
                      <h3 className="mt-4 text-sm font-medium text-gray-900">Memeriksa CV/resume yang ada...</h3>
                      <p className="mt-1 text-xs text-gray-500">Mohon tunggu sebentar.</p>
                    </div>
                  ) : isUploading ? (
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                      <Spinner className="h-12 w-12 text-primary" />
                      <h3 className="mt-4 text-sm font-medium text-gray-900">Mengunggah CV/resume...</h3>
                      <p className="mt-1 text-xs text-gray-500">Mohon tunggu sebentar.</p>
                    </div>
                  ) : !hasUploadedResume ? (
                    <div onDrop={onDrop} onDragOver={onDragOver} className="relative border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-primary transition-colors cursor-pointer">
                        <input type="file" accept=".pdf,.doc,.docx" onChange={handleResumeUpload} className="absolute inset-0 w-full h-full opacity-0 cursor-pointer" />
                        <UploadCloudIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900"><span className="text-primary">Klik untuk memilih file</span></h3>
                        <p className="mt-1 text-xs text-gray-500">PDF, DOC, DOCX (maks. 5MB)</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                            <FileTextIcon className="w-6 h-6 text-green-700"/>
                            <span className="font-medium text-green-800">{existingResume?.fileName.split('_').pop()}</span>
                        </div>
                        <button onClick={() => handleDeleteResume()} className="text-gray-500 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed" disabled={isDeleting}>
                            {isDeleting ? <Spinner className="w-5 h-5 text-primary" /> : <XIcon className="w-5 h-5"/>}
                        </button>
                    </div>
                  )}
                </div>
              ) : (
                <form className="space-y-6 animate-fade-in">
                  {/* Personal Details */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
                      <input id="fullName" type="text" value={formData.fullName} onChange={(e) => handleInputChange(e, 'fullName')} placeholder="Contoh: Budi Sanjaya" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.fullName ? 'border-red-500' : 'border-gray-300'}`} />
                      {errors.fullName && <p className="mt-1 text-xs text-red-600">{errors.fullName}</p>}
                    </div>
                    <div>
                      <label htmlFor="professionalTitle" className="block text-sm font-medium text-gray-700 mb-1">Jabatan / Peran yang Diinginkan</label>
                      <input id="professionalTitle" type="text" value={formData.professionalTitle} onChange={(e) => handleInputChange(e, 'professionalTitle')} placeholder="Contoh: Software Engineer" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.professionalTitle ? 'border-red-500' : 'border-gray-300'}`} />
                      {errors.professionalTitle && <p className="mt-1 text-xs text-red-600">{errors.professionalTitle}</p>}
                    </div>
                  </div>

                  {/* Professional Summary */}
                  <div>
                    <label htmlFor="professionalSummary" className="block text-sm font-medium text-gray-700 mb-1">Ringkasan Profesional</label>
                    <textarea id="professionalSummary" value={formData.professionalSummary} onChange={(e) => handleInputChange(e, 'professionalSummary')} placeholder="Tuliskan 2-3 kalimat tentang keahlian, pengalaman, dan tujuan karir Anda."
                      className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.professionalSummary ? 'border-red-500' : 'border-gray-300'}`} rows={4} />
                    {errors.professionalSummary && <p className="mt-1 text-xs text-red-600">{errors.professionalSummary}</p>}
                  </div>

                  {/* Most Recent Job */}
                  <div className="p-4 border border-gray-200 rounded-lg space-y-4">
                    <h3 className="font-semibold text-gray-800">Pengalaman Kerja Terakhir</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                       <div>
                         <label htmlFor="mostRecentJob.title" className="block text-sm font-medium text-gray-700 mb-1">Jabatan</label>
                         <input id="mostRecentJob.title" type="text" value={formData.mostRecentJob?.title || ''} onChange={(e) => handleInputChange(e, 'mostRecentJob.title')} placeholder="Contoh: Frontend Developer" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.title'] ? 'border-red-500' : 'border-gray-300'}`} />
                         {errors['mostRecentJob.title'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.title']}</p>}
                       </div>
                       <div>
                         <label htmlFor="mostRecentJob.company" className="block text-sm font-medium text-gray-700 mb-1">Perusahaan</label>
                         <input id="mostRecentJob.company" type="text" value={formData.mostRecentJob?.company || ''} onChange={(e) => handleInputChange(e, 'mostRecentJob.company')} placeholder="Contoh: PT Teknologi Maju" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.company'] ? 'border-red-500' : 'border-gray-300'}`} />
                         {errors['mostRecentJob.company'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.company']}</p>}
                       </div>
                    </div>
                     <div>
                        <label htmlFor="mostRecentJob.achievements" className="block text-sm font-medium text-gray-700 mb-1">Pencapaian Utama</label>
                        <textarea id="mostRecentJob.achievements" value={formData.mostRecentJob?.achievements || ''} onChange={(e) => handleInputChange(e, 'mostRecentJob.achievements')} placeholder="Gunakan poin-poin untuk menjelaskan 2-3 pencapaian utama Anda. Contoh: - Mengembangkan fitur X yang meningkatkan engagement user sebesar 20%."
                        className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.achievements'] ? 'border-red-500' : 'border-gray-300'}`} rows={4} />
                        {errors['mostRecentJob.achievements'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.achievements']}</p>}
                    </div>
                  </div>

                  {/* Skills */}
                  <div>
                    <label htmlFor="skills" className="block text-sm font-medium text-gray-700 mb-1">Keahlian Utama</label>
                    <input id="skills" type="text" value={formData.skills} onChange={(e) => handleInputChange(e, 'skills')} placeholder="Sebutkan 5-8 keahlian utama, pisahkan dengan koma. Contoh: React, TypeScript, Node.js" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.skills ? 'border-red-500' : 'border-gray-300'}`} />
                    {errors.skills && <p className="mt-1 text-xs text-red-600">{errors.skills}</p>}
                  </div>
                </form>
              )}

              <div className="mt-10">
                <div className="flex justify-end">
                  <button onClick={goNext} className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white p-3 sm:px-6 sm:py-3 rounded-lg font-semibold shadow-sm transition-colors">
                    <span className="inline">Selanjutnya</span>
                    <ArrowRightIcon className="w-5 h-5"/>
                  </button>
                </div>
                {/* General Validation Error */}
                {errors.general && (
                  <div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                    {errors.general}
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 2 && userPath === 'ai-powered' && (
             <div>
              <h2 className="text-2xl font-bold mb-2 text-gray-900">Langkah 2: Informasi Pekerjaan</h2>
              <p className="text-gray-600 mb-8">Berikan kami detail tentang pekerjaan yang Anda lamar.</p>

              <div className="flex w-full p-1 bg-gray-100 rounded-xl border-2 border-gray-200 mb-8">
                 <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${jobInputMethod === "text" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setJobInputMethod("text");
                    setErrors({});
                  }}
                >
                  <FileTextIcon className="w-5 h-5"/>
                  <span>Deskripsi Teks</span>
                </button>
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${jobInputMethod === "image" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setJobInputMethod("image");
                    setErrors({});
                  }}
                >
                  <UploadCloudIcon className="w-5 h-5"/>
                  <span>Poster Gambar</span>
                </button>
              </div>

              {jobInputMethod === "text" ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tempel deskripsi pekerjaan</label>
                  <textarea value={jobText} onChange={(e) => setJobText(e.target.value)} placeholder="Jelaskan detail lowongan pekerjaan..." className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary" rows={8}/>
                </div>
              ) : (
                <div>
                  {!jobImage ? (
                     <div onDrop={(e) => { e.preventDefault(); setJobImage(e.dataTransfer.files?.[0] || null); }} onDragOver={onDragOver} className="relative border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-primary transition-colors cursor-pointer">
                        <input type="file" accept="image/*" onChange={(e) => setJobImage(e.target.files?.[0] || null)} className="absolute inset-0 w-full h-full opacity-0 cursor-pointer" />
                        <UploadCloudIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900"><span className="text-primary">Klik untuk memilih gambar</span></h3>
                        <p className="mt-1 text-xs text-gray-500">PNG, JPG, JPEG (maks. 5MB)</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                            <FileTextIcon className="w-6 h-6 text-green-700"/>
                            <span className="font-medium text-green-800">{jobImage.name}</span>
                        </div>
                        <button onClick={() => setJobImage(null)} className="text-gray-500 hover:text-gray-800">
                            <XIcon className="w-5 h-5"/>
                        </button>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-between mt-10">
                <button onClick={goBack} className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 p-3 sm:px-6 sm:py-3 rounded-lg font-semibold shadow-sm transition-colors">
                    <ArrowLeftIcon className="w-5 h-5"/>
                    <span className="inline">Kembali</span>
                </button>
                <button
                  className="btn-primary rounded-lg"
                  onClick={async () => {
                      // Check if user is logged in first
                      if (!user) {
                        setLoginMode('register');
                        setShowLoginModal(true);
                        return;
                      }

                      // Basic validation for Step 2 inputs
                      if (jobInputMethod === "text" && !jobText.trim()) {
                        setToast({ show: true, message: "Deskripsi pekerjaan wajib diisi", type: "error" });
                        return;
                      }
                      if (jobInputMethod === "image" && !jobImage) {
                        setToast({ show: true, message: "Gambar deskripsi pekerjaan wajib diunggah", type: "error" });
                        return;
                      }

                      // Set default template (first recommended template)
                      const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
                      setSelectedTemplate(defaultTemplate);

                      // Move to Step 3 and start generation immediately
                      goNext();

                      try {
                        // Prepare simplified parameters for startGeneration
                        const params = {
                          resumeInputMethod,
                          jobInputMethod,
                          jobText: jobInputMethod === "text" ? jobText : undefined,
                          jobImage: jobInputMethod === "image" ? (jobImage || undefined) : undefined,
                          formData: resumeInputMethod === "scratch" ? formData : undefined,
                          unauthenticatedResumeFile: resumeInputMethod === "upload" && existingResume?.unauthenticatedResumeFile ? existingResume.unauthenticatedResumeFile : undefined,
                          selectedTemplate: defaultTemplate
                        };

                        const generationId = await startGeneration(params);
                        setGenerationId(generationId);
                        // Clear any previous local errors
                        setLocalGenerationError(null);
                      } catch (e) {
                        console.log(e);
                        // Immediately set generationId to null to stop any loading states
                        setGenerationId(null);
                        const errorMessage = (e as Error).message || "Gagal memulai AI";
                        
                        // Set local error state for immediate UI feedback
                        setLocalGenerationError(errorMessage);

                        // If it's a token error, only set hasTokenError flag (let error state UI handle display)
                        if (errorMessage.includes('Token Anda tidak cukup')) {
                          setHasTokenError(true);
                          // Don't show toast for token errors, the error state UI will handle it
                          setToast({ show: false, message: '', type: 'success' });
                        } else {
                          // For other errors, show toast
                          setToast({ show: true, message: errorMessage, type: "error" });
                        }
                      }
                    }}
                  disabled={isUploading || isLoading}
                >
                  <span className="flex items-center justify-center">
                    Buat CV -
                    <TokenIcon className="w-4 h-4 ml-0.5" />
                    20
                  </span>
                </button>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {userPath === 'manual' ? 'Edit CV Anda' : 'Langkah 3: Hasil & Edit'}
                  </h2>
                  <p className="text-gray-600 mt-2">
                    {userPath === 'manual'
                      ? "Mulai dari template kosong dan buat CV sesuai keinginan Anda"
                      : structuredData && generation.status === "done"
                        ? "Edit dan sesuaikan CV Anda"
                        : "AI sedang membuat CV Anda berdasarkan informasi yang telah diberikan"}
                  </p>
                </div>
              </div>

              {/* Show insufficient tokens banner for manual context only - download banners are shown below download buttons */}
              {insufficientTokensError.show && insufficientTokensError.context === 'manual' && (
                <InsufficientTokensBanner
                  show={insufficientTokensError.show}
                  message={insufficientTokensError.message}
                  onDismiss={handleDismissBanner}
                  onBuyTokens={handleBuyTokens}
                />
              )}
              
              {/* Show manual resume creation progress */}
              {userPath === 'manual' && isCreatingManualResume && (
                <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                  <Spinner className="h-12 w-12 text-primary mb-4" />
                  <h3 className="text-xl font-semibold text-gray-800">Menyiapkan CV...</h3>
                  <p className="text-gray-600 mt-2">Sedang membuat struktur CV</p>
                </div>
              )}

              {/* Show fallback error state when hasTokenError is true but banner is dismissed */}
              {!structuredData && userPath === 'ai-powered' && hasTokenError && !insufficientTokensError.show && (
                <div className="py-8">
                  <div className="flex flex-col items-center justify-center py-12 text-center animate-fade-in">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                      <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-red-800 mb-2">Token Tidak Cukup</h3>
                    <p className="text-gray-600 mb-6 max-w-md">
                      Anda tidak memiliki cukup token untuk membuat CV
                    </p>
                    <div className="flex gap-4">
                      <button
                        onClick={() => {
                          setInsufficientTokensError({ show: false, message: '', context: 'download' });
                          setHasTokenError(false);
                          setGenerationId(null);
                          setStructuredData(null);
                          setLiveHtmlContent("");
                          goBack();
                        }}
                        className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                      >
                        <ArrowLeftIcon className="w-5 h-5"/>
                        <span>Kembali</span>
                      </button>
                      <button
                        onClick={handleBuyTokens}
                        className="inline-flex items-center gap-2 px-6 py-3 bg-primary hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
                      >
                        <TokenIcon className="w-5 h-5" />
                        Beli Token
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Priority-based state rendering for AI-powered path: Error states take precedence over loading states */}
              {!structuredData && userPath === 'ai-powered' && !hasTokenError && (
                <>
                  {/* PRIORITY 1: Show error states first (local errors OR generation errors) */}
                  {(localGenerationError || generation.status === "error") ? (
                    <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <XIcon className="w-8 h-8 text-red-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-red-800 mb-2">Oops! Terjadi Kesalahan</h3>
                      <p className="text-gray-600 mb-6 max-w-md">
                        {localGenerationError || (generation.status === "error" ? generation.error : null) || "Maaf, terjadi kesalahan saat membuat CV Anda. Silakan coba lagi atau kembali ke langkah sebelumnya."}
                      </p>
                      <div className="flex gap-4">
                        <button
                          onClick={() => {
                            setGenerationId(null);
                            setHasTokenError(false);
                            setLocalGenerationError(null);
                            setStructuredData(null);
                            setLiveHtmlContent("");
                            goBack();
                          }}
                          className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                        >
                          <ArrowLeftIcon className="w-5 h-5"/>
                          <span>Kembali</span>
                        </button>
                        <button
                          className="btn-primary rounded-lg"
                          onClick={async () => {
                            // Clear error states immediately to show loading state
                            setLocalGenerationError(null);
                            setHasTokenError(false);
                            setInsufficientTokensError({ show: false, message: '', context: 'download' });
                            
                            // Retry generation with same parameters
                            try {
                              const params = {
                                resumeInputMethod,
                                jobInputMethod,
                                jobText: jobInputMethod === "text" ? jobText : undefined,
                                jobImage: jobInputMethod === "image" ? (jobImage || undefined) : undefined,
                                formData: resumeInputMethod === "scratch" ? formData : undefined,
                                unauthenticatedResumeFile: resumeInputMethod === "upload" && existingResume?.unauthenticatedResumeFile ? existingResume.unauthenticatedResumeFile : undefined,
                                selectedTemplate: selectedTemplate || undefined
                              };

                              const generationId = await startGeneration(params);
                              setGenerationId(generationId);
                            } catch (e) {
                              console.log(e);
                              const errorMessage = (e as Error).message || "Gagal memulai ulang AI";
                              setLocalGenerationError(errorMessage);
                              
                              // If it's a token error, show token error state instead of toast
                              if (errorMessage.includes('Token Anda tidak cukup')) {
                                setHasTokenError(true);
                              } else {
                                setToast({ show: true, message: errorMessage, type: "error" });
                              }
                            }
                          }}
                        >
                          <span className="flex items-center justify-center">
                            Coba Lagi -
                            <TokenIcon className="w-4 h-4 ml-0.5" />
                            25
                          </span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    /* PRIORITY 2: Show loading states only when no errors exist */
                    <>
                      {generation.status === "processing" && (
                        <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                          <Spinner className="h-12 w-12 text-primary mb-4" />
                          <h3 className="text-xl font-semibold text-gray-800">AI sedang bekerja...</h3>
                          <p className="text-gray-600 mt-2">CV Anda sedang dibuat. Proses ini mungkin memakan waktu sejenak</p>
                        </div>
                      )}

                      {generation.status === "idle" && (
                        <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                          <Spinner className="h-12 w-12 text-primary mb-4" />
                          <h3 className="text-xl font-semibold text-gray-800">Memulai proses...</h3>
                          <p className="text-gray-600 mt-2">Sedang menyiapkan untuk membuat CV Anda.</p>
                        </div>
                      )}

                      {/* Show back button only for idle state and no errors */}
                      {generation.status === "idle" && (
                        <div className="flex justify-between mt-8">
                          <button
                            onClick={() => {
                              setGenerationId(null);
                              setHasTokenError(false);
                              setLocalGenerationError(null);
                              setStructuredData(null);
                              setLiveHtmlContent("");
                              goBack();
                            }}
                            className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-4 py-2 rounded-lg font-semibold transition-colors"
                          >
                              <ArrowLeftIcon className="w-5 h-5"/>
                              <span>Kembali</span>
                          </button>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}

              {/* Show editing interface when generation is complete and not creating manual resume */}
              {structuredData && !isCreatingManualResume && (
                <>
                  <div className="animate-fade-in">
                    {/* Desktop Layout - Template Selector (hidden on mobile) */}
                    <div className="hidden md:block mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Pilih Template</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {resumeTemplates.map((template) => {
                          const isSelected = selectedTemplate?.id === template.id;
                          const isProcessing = isSelectingTemplate && template.id !== selectedTemplate?.id;
                          return (
                            <button
                              key={template.id}
                              onClick={() => handleTemplateSelection(template)}
                              disabled={isSelectingTemplate}
                              className={`relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                                isSelected
                                  ? "border-primary bg-primary/5"
                                  : "border-gray-200 hover:border-gray-300"
                              } ${isSelectingTemplate ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              <div className="text-center">
                                <div className="w-full aspect-[210/297] bg-white rounded mb-2 border border-gray-200 overflow-hidden p-1 relative">
                                  <TemplatePreviewThumbnail template={template} />
                                  {isProcessing && (
                                    <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                                      <Spinner className="w-6 h-6 text-primary" />
                                    </div>
                                  )}
                                </div>
                                <h4 className={`font-semibold text-sm ${isSelected ? 'text-primary' : 'text-gray-900'}`}>
                                  {template.name}
                                </h4>
                                {isSelected && !isSelectingTemplate && (
                                  <div className="absolute top-1 right-1">
                                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                      <CheckIcon className="w-2.5 h-2.5 text-white" />
                                    </div>
                                  </div>
                                )}
                                {isSelectingTemplate && template.id === selectedTemplate?.id && (
                                  <div className="absolute top-1 right-1">
                                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                      <Spinner className="w-2.5 h-2.5 text-white" />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </button>
                          );
                        })}

                        {/* Coming Soon Placeholder */}
                        <div className="relative p-3 rounded-lg border border-dashed border-gray-300 bg-gray-50">
                          <div className="text-center">
                            <div className="w-full aspect-[210/297] bg-gray-100 rounded mb-2 border border-gray-200 flex items-center justify-center">
                            </div>
                            <h4 className="font-semibold text-sm text-gray-500">
                              Segera Hadir
                            </h4>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Mobile-First Layout: Preview Only on Mobile, Side by Side on Desktop */}
                    <div className="md:gap-6 space-y-6">
                      {/* Preview Section - Always visible */}
                      <div className="md:order-1">
                        <div className="mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">Pratinjau</h3>
                          <span className="text-sm text-gray-600">{selectedTemplate?.name}</span>
                        </div>



                        <div className="bg-gray-50 rounded-lg p-2 h-full w-full">
                          {liveHtmlContent ? (
                            <ResumePreview
                              htmlContent={liveHtmlContent}
                              onOverflowDetected={handleOverflowDetected}
                            />
                          ) : (
                            <div className="aspect-[210/297] bg-gray-100 rounded-lg flex items-center justify-center text-gray-400 border">
                              <div className="text-center px-4">
                                {userPath === 'manual' ? (
                                  <>
                                    <FileTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                                    <h4 className="text-sm font-medium text-gray-500 mb-1">CV Kosong</h4>
                                    <p className="text-xs text-gray-400">Isi data personal dan posisi target di bagian "Edit" untuk melihat pratinjau CV</p>
                                  </>
                                ) : (
                                  <>
                                    <Spinner className="w-8 h-8 mx-auto mb-2" />
                                    <span>Membuat pratinjau...</span>
                                  </>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Overflow Warning - Below Preview */}
                        {isContentOverflowing && (
                          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg flex items-start gap-3">
                            <ExclamationTriangleIcon className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                            <div className="flex-1">
                              <p className="text-sm font-medium text-amber-800">
                                Konten Melebihi Satu Halaman
                              </p>
                              <p className="text-sm text-amber-700 mt-1">
                                Silakan sesuaikan konten agar muat dalam satu halaman
                              </p>
                            </div>
                          </div>
                        )}

                        {/* Mobile Download Button - Below Preview */}
                        <div className="mt-4 md:hidden">
                          {generation.status === "done" ? (
                            hasRequiredFields ? (
                              <button
                                className="w-full btn-primary rounded-lg"
                                onClick={handleDownload}
                                disabled={isDownloadingPdf}
                              >
                                {isDownloadingPdf ? (
                                  <span className="flex items-center justify-center">
                                    <Spinner className="w-5 h-5 mr-2" />
                                    Mengunduh...
                                  </span>
                                ) : (
                                  <span className="flex items-center justify-center">
                                    <DownloadIcon className="w-5 h-5 mr-2" />
                                    Unduh PDF
                                    {userPath === 'manual' && !tokensDeducted && (
                                      <>
                                        {' '}-{' '}
                                        <TokenIcon className="w-4 h-4 ml-0.5" />
                                        15
                                      </>
                                    )}
                                  </span>
                                )}
                              </button>
                            ) : (
                              <button
                                disabled
                                className="w-full bg-gray-400 cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
                              >
                                <span className="flex items-center justify-center">
                                  <DownloadIcon className="w-5 h-5 mr-2" />
                                  Unduh PDF
                                  {userPath === 'manual' && !tokensDeducted && (
                                    <>
                                      {' '}-{' '}
                                      <TokenIcon className="w-4 h-4 ml-0.5" />
                                      15
                                    </>
                                  )}
                                </span>
                              </button>
                            )
                          ) : (
                            hasRequiredFields ? (
                              <button
                                className="w-full btn-primary rounded-lg"
                                onClick={handleDownload}
                                disabled={!generationId}
                              >
                                <span className="flex items-center justify-center">
                                  <DownloadIcon className="w-5 h-5 mr-2" />
                                  Buat & Unduh PDF
                                  {userPath === 'manual' && !tokensDeducted && (
                                    <>
                                      {' '}-{' '}
                                      <TokenIcon className="w-4 h-4 ml-0.5" />
                                      15
                                    </>
                                  )}
                                </span>
                              </button>
                            ) : (
                              <button
                                disabled
                                className="w-full bg-gray-400 cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
                              >
                                <span className="flex items-center justify-center">
                                  <DownloadIcon className="w-5 h-5 mr-2" />
                                  Buat & Unduh PDF
                                  {userPath === 'manual' && !tokensDeducted && (
                                    <>
                                      {' '}-{' '}
                                      <TokenIcon className="w-4 h-4 ml-0.5" />
                                      15
                                    </>
                                  )}
                                </span>
                              </button>
                            )
                          )}
      
                          {/* Show insufficient tokens banner below download button for desktop */}
                          {insufficientTokensError.show && insufficientTokensError.context === 'download' && (
                            <div className="mt-4">
                              <InsufficientTokensBanner
                                show={insufficientTokensError.show}
                                message={insufficientTokensError.message}
                                onDismiss={handleDismissBanner}
                                onBuyTokens={handleBuyTokens}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Edit Section - Hidden on mobile, visible on desktop */}
                      <div className="hidden md:block md:order-2">
                        <div className="mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">Edit CV</h3>
                          <p className="text-sm text-gray-600">Buat perubahan dan lihat hasilnya secara langsung</p>
                        </div>
                        <div className="rounded-lg h-full overflow-y-auto">
                          <ResumeEditForm
                            data={structuredData}
                            onChange={handleStructuredDataChange}
                            onAutoSave={handleAutoSave}
                            autoSaveDelay={1000}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Bottom Navigation */}
                  <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-3 py-4 pb-safe sm:px-4 md:hidden">
                    <div className="flex justify-between items-center gap-2 sm:gap-3">
                      {/* Template Button */}
                      <button
                        onClick={() => setIsTemplateSheetOpen(true)}
                        className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium transition-colors"
                      >
                        <TemplateIcon className="w-5 h-5" />
                        <span>Template</span>
                      </button>

                      {/* Edit Button */}
                      <button
                        onClick={() => setIsEditSheetOpen(true)}
                        className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium transition-colors"
                      >
                        <PencilIcon className="w-5 h-5" />
                        <span>Edit</span>
                      </button>
                    </div>
                  </div>

                  {/* Desktop Action Buttons */}
                  <div className="hidden md:block mt-6">
                    <div className="flex justify-end">
                      {generation.status === "done" ? (
                        hasRequiredFields ? (
                          <button
                            className="btn-primary rounded-lg"
                            onClick={handleDownload}
                            disabled={isDownloadingPdf}
                          >
                            {isDownloadingPdf ? (
                              <span className="flex items-center justify-center">
                                <Spinner className="w-5 h-5 mr-2" />
                                Mengunduh...
                              </span>
                            ) : (
                              <span className="flex items-center justify-center">
                                <DownloadIcon className="w-5 h-5 mr-2" />
                                Unduh PDF
                                {userPath === 'manual' && !tokensDeducted && (
                                  <>
                                    {' '}-{' '}
                                    <TokenIcon className="w-4 h-4 ml-0.5" />
                                    15
                                  </>
                                )}
                              </span>
                            )}
                          </button>
                        ) : (
                          <button
                            disabled
                            className="bg-gray-400 cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
                          >
                            <span className="flex items-center justify-center">
                              <DownloadIcon className="w-5 h-5 mr-2" />
                              Unduh PDF
                              {userPath === 'manual' && !tokensDeducted && (
                                <>
                                  {' '}-{' '}
                                  <TokenIcon className="w-4 h-4 ml-0.5" />
                                  15
                                </>
                              )}
                            </span>
                          </button>
                        )
                      ) : (
                        hasRequiredFields ? (
                          <button
                            className="btn-primary rounded-lg"
                            onClick={handleDownload}
                            disabled={!generationId}
                          >
                            <span className="flex items-center justify-center">
                              <DownloadIcon className="w-5 h-5 mr-2" />
                              Buat & Unduh
                              {userPath === 'manual' && !tokensDeducted && (
                                <>
                                  {' '}-{' '}
                                  <TokenIcon className="w-4 h-4 ml-0.5" />
                                  15
                                </>
                              )}
                            </span>
                          </button>
                        ) : (
                          <button
                            disabled
                            className="bg-gray-400 cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
                          >
                            <span className="flex items-center justify-center">
                              <DownloadIcon className="w-5 h-5 mr-2" />
                              Buat & Unduh
                              {userPath === 'manual' && !tokensDeducted && (
                                <>
                                  {' '}-{' '}
                                  <TokenIcon className="w-4 h-4 ml-0.5" />
                                  15
                                </>
                              )}
                            </span>
                          </button>
                        )
                      )}
                    </div>

                    {/* Desktop Banner Container - Below download button with proper spacing */}
                    {insufficientTokensError.show && insufficientTokensError.context === 'download' && (
                      <div className="mt-6">
                        <InsufficientTokensBanner
                          show={insufficientTokensError.show}
                          message={insufficientTokensError.message}
                          onDismiss={handleDismissBanner}
                          onBuyTokens={handleBuyTokens}
                        />
                      </div>
                    )}
                  </div>

                  {/* Template Selection Bottom Sheet */}
                  <BottomSheet
                    isOpen={isTemplateSheetOpen}
                    onClose={() => setIsTemplateSheetOpen(false)}
                    title="Pilih Template"
                  >
                    <div className="grid grid-cols-2 gap-4">
                      {resumeTemplates.map((template) => {
                        const isSelected = selectedTemplate?.id === template.id;
                        const isProcessing = isSelectingTemplate && template.id !== selectedTemplate?.id;
                        return (
                          <button
                            key={template.id}
                            onClick={async () => {
                              if (template.id === selectedTemplate?.id) {
                                setIsTemplateSheetOpen(false);
                                return;
                              }

                              const success = await handleTemplateSelection(template);
                              if (success) {
                                setIsTemplateSheetOpen(false);
                              }
                            }}
                            disabled={isSelectingTemplate}
                            className={`relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                              isSelected
                                ? "border-primary bg-primary/5"
                                : "border-gray-200 hover:border-gray-300"
                            } ${isSelectingTemplate ? 'opacity-50 cursor-not-allowed' : ''}`}
                          >
                            <div className="text-center">
                              <div className="w-full aspect-[210/297] bg-white rounded mb-2 border border-gray-200 overflow-hidden p-1 relative">
                                <TemplatePreviewThumbnail template={template} />
                                {isProcessing && (
                                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                                    <Spinner className="w-6 h-6 text-primary" />
                                  </div>
                                )}
                              </div>
                              <h4 className={`font-semibold text-xs ${isSelected ? 'text-primary' : 'text-gray-900'}`}>
                                {template.name}
                              </h4>
                              {isSelected && !isSelectingTemplate && (
                                <div className="absolute top-1 right-1">
                                  <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                    <CheckIcon className="w-2.5 h-2.5 text-white" />
                                  </div>
                                </div>
                              )}
                              {isSelectingTemplate && template.id === selectedTemplate?.id && (
                                <div className="absolute top-1 right-1">
                                  <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                    <Spinner className="w-2.5 h-2.5 text-white" />
                                  </div>
                                </div>
                              )}
                            </div>
                          </button>
                        );
                      })}

                      {/* Coming Soon Placeholder */}
                      <div className="relative p-3 rounded-lg border border-dashed border-gray-300 bg-gray-50">
                        <div className="text-center">
                          <div className="w-full aspect-[210/297] bg-gray-100 rounded mb-2 border border-gray-200 flex items-center justify-center">
                          </div>
                          <h4 className="font-semibold text-xs text-gray-500">
                            Segera Hadir
                          </h4>
                        </div>
                      </div>
                    </div>
                  </BottomSheet>

                  {/* Edit Form Bottom Sheet */}
                  <BottomSheet
                    isOpen={isEditSheetOpen}
                    onClose={() => setIsEditSheetOpen(false)}
                    title="Edit CV"
                  >
                    <ResumeEditForm
                      data={structuredData}
                      onChange={handleStructuredDataChange}
                      onAutoSave={handleAutoSave}
                      autoSaveDelay={1000}
                    />
                  </BottomSheet>
                </>
              )}
            </div>
          )}
            </div>
          </>
        )}
      </div>

      {/* Login/Register Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex flex-row items-center justify-between gap-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {loginMode === 'login' ? 'Masuk ke Akun' : 'Daftar Akun Baru'}
                </h3>
                <button
                  onClick={() => {
                    setShowLoginModal(false);
                    setLoginError('');
                    setLoginSuccess('');
                    setLoginForm({ email: '', password: '', confirmPassword: '' });
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XIcon className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              <p className="text-sm text-gray-600 mb-4">
                {loginMode === 'login'
                  ? 'Masuk untuk membuat CV ATS-friendly Anda'
                  : 'Daftar untuk membuat CV ATS-friendly Anda'
                }
              </p>

              {loginSuccess && (
                <div className="mb-4 p-3 bg-green-100 border border-green-200 text-green-700 rounded-md text-sm">
                  {loginSuccess}
                </div>
              )}

              {loginError && (
                <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md text-sm">
                  {loginError}
                </div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={loginForm.email}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                    disabled={isLoggingIn}
                  />
                </div>

                {loginMode === 'register' && (
                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                      Konfirmasi Password
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      value={loginForm.confirmPassword}
                      onChange={(e) => setLoginForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                      disabled={isLoggingIn}
                    />
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isLoggingIn}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoggingIn ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {loginMode === 'login' ? 'Masuk...' : 'Mendaftar...'}
                    </>
                  ) : (
                    loginMode === 'login' ? 'Masuk' : 'Daftar'
                  )}
                </button>
              </form>

              <div className="mt-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">atau</span>
                  </div>
                </div>

                <button
                  onClick={handleGoogleLogin}
                  disabled={isLoggingIn}
                  className="mt-3 w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  Masuk dengan Google
                </button>
              </div>

              <div className="mt-4 text-center">
                <button
                  onClick={() => {
                    setLoginMode(loginMode === 'login' ? 'register' : 'login');
                    setLoginError('');
                    setLoginSuccess('');
                  }}
                  className="text-sm text-blue-600 hover:text-blue-500"
                  disabled={isLoggingIn}
                >
                  {loginMode === 'login'
                    ? 'Belum punya akun? Daftar di sini'
                    : 'Sudah punya akun? Masuk di sini'
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <Footer />

    </main>
  );
}
