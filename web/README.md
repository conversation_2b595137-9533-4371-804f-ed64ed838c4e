# Gigsta - Local Development Setup

This guide will help you set up the Gigsta web application for local development.

## Prerequisites

Before you begin, make sure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager
- **Git** for version control

## 1. Clone the Repository

```bash
git clone <repository-url>
cd GigstaProject/web
```

## 2. Install Dependencies

```bash
npm install
# or
yarn install
```

## 3. Supabase Setup

### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new account if you don't have one
2. Click "New Project" and fill in the details:
   - **Name**: Choose a name for your project (e.g., "gigsta-local")
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose the closest region to your location
3. Wait for the project to be created (this may take a few minutes)

### Get Your Supabase Keys

Once your project is ready:

1. Go to **Settings** → **API** in your Supabase dashboard
2. Copy the following values:
   - **Project URL** (this will be your `NEXT_PUBLIC_SUPABASE_URL`)
   - **anon public** key (this will be your `NEXT_PUBLIC_SUPABASE_ANON_KEY`)
   - **service_role** key (this will be your `SUPABASE_SERVICE_ROLE_KEY`)

### Set Up Database Schema

1. Go to **SQL Editor** in your Supabase dashboard
2. Run the following SQL to create the necessary tables:

```sql
-- Enable RLS (Row Level Security)
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Create profiles table
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  token_balance INTEGER DEFAULT 0,
  resume_text TEXT,
  resume_file_name TEXT,
  resume_file_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create purchases table
CREATE TABLE public.purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  package_name TEXT NOT NULL,
  token_amount INTEGER NOT NULL,
  price_amount INTEGER NOT NULL,
  payment_status TEXT DEFAULT 'pending',
  payment_method TEXT,
  transaction_id TEXT,
  midtrans_order_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own purchases" ON public.purchases
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own purchases" ON public.purchases
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### Configure Storage

1. Go to **Storage** in your Supabase dashboard
2. Create a new bucket called `resumes`
3. Set the bucket to **Public** if you want resume files to be publicly accessible
4. Set up storage policies:

```sql
-- Allow authenticated users to upload resumes
CREATE POLICY "Users can upload own resumes" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'resumes' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to view own resumes
CREATE POLICY "Users can view own resumes" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'resumes' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

## 4. Environment Variables Setup

Create a `.env.local` file in the `web` directory with the following variables:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# AI API Keys (Required for AI features)
GOOGLE_AI_API_KEY=your-google-ai-api-key
OPENAI_API_KEY=your-openai-api-key

# Payment Integration (Midtrans) - Optional for local development
MIDTRANS_SERVER_KEY=your-midtrans-server-key
MIDTRANS_CLIENT_KEY=your-midtrans-client-key
MIDTRANS_IS_PRODUCTION=false

# Analytics (Optional for local development)
NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN=your-mixpanel-token
ROLLBAR_ACCESS_TOKEN=your-rollbar-token

# Application Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Getting API Keys

#### Google AI API Key
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create a new API key
3. Copy the key and add it to your `.env.local` file

#### OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account and navigate to API keys
3. Create a new API key
4. Copy the key and add it to your `.env.local` file

#### Midtrans (Optional - for payment testing)
1. Go to [Midtrans Dashboard](https://dashboard.midtrans.com/)
2. Create an account and get your sandbox keys
3. Add the server key and client key to your `.env.local` file

## 5. Run the Application

```bash
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:3000`.

## 6. Verify Setup

1. **Database Connection**: Check if the app loads without database errors
2. **Authentication**: Try creating a new account to test Supabase Auth
3. **File Upload**: Test resume upload functionality
4. **AI Features**: Test the AI generation features (requires API keys)

## 7. Local Supabase (Alternative Setup)

If you prefer to run Supabase locally using Docker:

### Install Supabase CLI

```bash
npm install -g supabase
# or
brew install supabase/tap/supabase
```

### Initialize Local Supabase

```bash
supabase init
supabase start
```

This will start local Supabase services and provide you with local URLs and keys to use in your `.env.local` file.

### Local Environment Variables

When using local Supabase, update your `.env.local`:

```bash
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-local-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-local-service-role-key
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify your Supabase URL and keys are correct
   - Check if your Supabase project is active

2. **Authentication Issues**
   - Ensure RLS policies are set up correctly
   - Check if the `handle_new_user()` function is working

3. **File Upload Issues**
   - Verify storage bucket exists and is configured
   - Check storage policies are set up correctly

4. **AI Generation Errors**
   - Ensure API keys are valid and have sufficient credits
   - Check API key permissions and quotas

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Review the Supabase dashboard logs
3. Verify all environment variables are set correctly
4. Ensure all required services are running

## Project Structure

```
web/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── generate-application-letter/
│   │   ├── generate-email-application/
│   │   ├── generate-job-matching/
│   │   └── payment/              # Payment processing
│   ├── application-letter/       # Application letter page
│   ├── email-application/        # Email application page
│   ├── job-match/               # Job matching page
│   ├── auth/                    # Authentication pages
│   ├── buy-tokens/              # Token purchase
│   └── profile/                 # User profile
├── components/                   # Reusable UI components
├── hooks/                       # Custom React hooks
├── lib/                         # Utility libraries
│   └── supabase.ts             # Supabase client configuration
├── utils/                       # Utility functions
├── types/                       # TypeScript type definitions
└── public/                      # Static assets
```

## Next Steps

After completing the setup:

1. **Test Authentication**: Create a test account to verify Supabase Auth is working
2. **Upload a Resume**: Test the file upload functionality
3. **Try AI Features**: Test the application letter and email generation (requires API keys)
4. **Explore the Code**: Familiarize yourself with the project structure
5. **Make Changes**: Start developing new features or fixing issues

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Google AI Studio](https://aistudio.google.com/)
- [OpenAI Platform](https://platform.openai.com/)

---

**Happy coding!** 🚀
